#include "led_charge.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "wifi_ap.h"
#include "file_server.h"

#define GPIO_CHARGE_LED GPIO_NUM_7     // LED GPIO引脚 (默认GPIO2)
#define GPIO_POWER_SOURCE GPIO_NUM_10  // 供电来源 -> 低电平:外部供电(打开文件服务)   高电平:电池供电
#define GPIO_CHARGE_STATUS GPIO_NUM_18 // 显示充电状态 -> 闪烁:充电中  常亮:充电完成
#define BLINK_INTERVAL_MS 500          // 闪烁间隔 (毫秒)

static const char *TAG = "CHARGE";
static bool led_initialized = false;
static led_state_t current_led_state = LED_STATE_OFF;
static TaskHandle_t charge_task_handle = NULL;

/* OTA service management */
static bool ota_services_running = false;
static power_source_t last_power_source = POWER_SOURCE_BATTERY;
static esp_timer_handle_t power_debounce_timer = NULL;

/* Base path for file server */
#define BASE_PATH "/data"

/* Debounce time in microseconds (100ms) */
#define POWER_DEBOUNCE_TIME_US 100000

/* Forward declarations */
static void power_debounce_timer_callback(void *arg);

static esp_err_t led_charge_init(void);

/**
 * @brief 读取电源来源状态
 */
power_source_t get_power_source_status(void)
{
    int level = gpio_get_level(GPIO_POWER_SOURCE);
    return (level == 0) ? POWER_SOURCE_EXTERNAL : POWER_SOURCE_BATTERY;
}

/**
 * @brief 读取充电状态
 */
charge_status_t get_charge_status(void)
{
    int level = gpio_get_level(GPIO_CHARGE_STATUS);
    return (level == 0) ? CHARGE_STATUS_COMPLETE : CHARGE_STATUS_CHARGING;
}

/**
 * @brief 启动OTA服务 (WiFi AP和文件服务器)
 */
esp_err_t local_ota_start_services(void)
{
    if (ota_services_running)
    {
        ESP_LOGW(TAG, "OTA services already running");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Starting WiFi AP and file server...");

    /* Start WiFi AP */
    esp_err_t ret = wifi_init_ap();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to start WiFi AP: %s", esp_err_to_name(ret));
        return ret;
    }

    /* Start file server */
    ret = start_file_server(BASE_PATH);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to start file server: %s", esp_err_to_name(ret));
        /* Try to stop WiFi AP on failure */
        wifi_deinit_ap();
        return ret;
    }

    ota_services_running = true;
    ESP_LOGI(TAG, "OTA services started successfully");
    return ESP_OK;
}

/**
 * @brief 停止OTA服务 (WiFi AP和文件服务器)
 */
esp_err_t local_ota_stop_services(void)
{
    if (!ota_services_running)
    {
        ESP_LOGW(TAG, "OTA services not running");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Stopping WiFi AP and file server...");

    /* Stop file server */
    esp_err_t ret1 = stop_file_server();
    if (ret1 != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to stop file server: %s", esp_err_to_name(ret1));
    }

    /* Stop WiFi AP */
    esp_err_t ret2 = wifi_deinit_ap();
    if (ret2 != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to stop WiFi AP: %s", esp_err_to_name(ret2));
    }

    ota_services_running = false;
    ESP_LOGI(TAG, "OTA services stopped");

    /* Return error if either operation failed */
    return (ret1 == ESP_OK && ret2 == ESP_OK) ? ESP_OK : ESP_FAIL;
}

/**
 * @brief 电源状态防抖定时器回调
 */
static void power_debounce_timer_callback(void *arg)
{
    power_source_t current_power_source = get_power_source_status();

    if (current_power_source != last_power_source)
    {
        ESP_LOGI(TAG, "Power source changed: %s -> %s",
                 last_power_source == POWER_SOURCE_EXTERNAL ? "EXTERNAL" : "BATTERY",
                 current_power_source == POWER_SOURCE_EXTERNAL ? "EXTERNAL" : "BATTERY");

        last_power_source = current_power_source;

        if (current_power_source == POWER_SOURCE_EXTERNAL)
        {
            /* External power detected - start OTA services */
            esp_err_t ret = local_ota_start_services();
            if (ret != ESP_OK)
            {
                ESP_LOGE(TAG, "Failed to start OTA services: %s", esp_err_to_name(ret));
            }
        }
        else
        {
            /* Battery power detected - stop OTA services */
            esp_err_t ret = local_ota_stop_services();
            if (ret != ESP_OK)
            {
                ESP_LOGE(TAG, "Failed to stop OTA services: %s", esp_err_to_name(ret));
            }
        }
    }
}

esp_err_t led_charge_init(void)
{
    esp_err_t ret;

    if (led_initialized)
    {
        ESP_LOGW(TAG, "LED already initialized");
        return ESP_OK;
    }

    // 配置LED GPIO为输出模式
    gpio_config_t led_io_conf = {
        .intr_type = GPIO_INTR_DISABLE,            // 禁用中断
        .mode = GPIO_MODE_OUTPUT,                  // 输出模式
        .pin_bit_mask = (1ULL << GPIO_CHARGE_LED), // GPIO_CHARGE_LED
        .pull_down_en = 0,                         // 禁用下拉
        .pull_up_en = 0,                           // 禁用上拉
    };

    ret = gpio_config(&led_io_conf);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "LED GPIO config failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 配置电源来源GPIO为输入模式
    gpio_config_t power_source_io_conf = {
        .intr_type = GPIO_INTR_DISABLE,              // 禁用中断
        .mode = GPIO_MODE_INPUT,                     // 输入模式
        .pin_bit_mask = (1ULL << GPIO_POWER_SOURCE), // GPIO_POWER_SOURCE
        .pull_down_en = 0,                           // 禁用下拉
        .pull_up_en = 1,                             // 启用上拉
    };

    ret = gpio_config(&power_source_io_conf);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Power source GPIO config failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 配置充电状态GPIO为输入模式
    gpio_config_t charge_status_io_conf = {
        .intr_type = GPIO_INTR_DISABLE,               // 禁用中断
        .mode = GPIO_MODE_INPUT,                      // 输入模式
        .pin_bit_mask = (1ULL << GPIO_CHARGE_STATUS), // GPIO_CHARGE_STATUS
        .pull_down_en = 0,                            // 禁用下拉
        .pull_up_en = 1,                              // 启用上拉
    };

    ret = gpio_config(&charge_status_io_conf);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Charge status GPIO config failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 初始状态设为低电平（LED关闭）
    ret = gpio_set_level(GPIO_CHARGE_LED, LED_STATE_OFF);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to set initial LED state: %s", esp_err_to_name(ret));
        return ret;
    }
    current_led_state = LED_STATE_OFF;

    // 创建电源状态防抖定时器
    esp_timer_create_args_t timer_args = {
        .callback = power_debounce_timer_callback,
        .arg = NULL,
        .name = "power_debounce"};

    ret = esp_timer_create(&timer_args, &power_debounce_timer);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to create power debounce timer: %s", esp_err_to_name(ret));
        return ret;
    }

    // 初始化电源状态
    last_power_source = get_power_source_status();

    led_initialized = true;

    return ESP_OK;
}

/**
 * @brief 显示充电状态和监控电源变化
 */
static void led_charge_task_callback(void *pvParameters)
{
    while (1)
    {
        // 读取GPIO状态
        power_source_t power_source = get_power_source_status();
        charge_status_t charge_status = get_charge_status();

        // 检查电源状态变化并启动防抖定时器
        if (power_source != last_power_source)
        {
            // 启动防抖定时器
            esp_timer_start_once(power_debounce_timer, POWER_DEBOUNCE_TIME_US);
        }

        // 根据需求实现LED控制逻辑
        // 当GPIO_POWER_SOURCE为低电平（外部供电）时：
        if (power_source == POWER_SOURCE_EXTERNAL)
        {
            // 如果GPIO_CHARGE_STATUS为低电平（充电完成）：LED常亮
            if (charge_status == CHARGE_STATUS_COMPLETE)
            {
                gpio_set_level(GPIO_CHARGE_LED, LED_STATE_ON);
                current_led_state = LED_STATE_ON;
                ESP_LOGI(TAG, "External power, charge complete - LED ON");
            }
            // 否则（充电中）：LED闪烁
            else
            {
                // 切换LED状态实现闪烁
                current_led_state = (current_led_state == LED_STATE_ON) ? LED_STATE_OFF : LED_STATE_ON;
                gpio_set_level(GPIO_CHARGE_LED, current_led_state);
                ESP_LOGI(TAG, "External power, charging - LED BLINK");
            }
        }
        // 当GPIO_POWER_SOURCE为高电平（电池供电）时：LED关闭
        else
        {
            gpio_set_level(GPIO_CHARGE_LED, LED_STATE_OFF);
            current_led_state = LED_STATE_OFF;
            ESP_LOGI(TAG, "Battery power - LED OFF");
        }

        // 延时
        vTaskDelay(pdMS_TO_TICKS(BLINK_INTERVAL_MS));
    }
}

/**
 * @brief 初始化并创建充电状态任务（一键启动）
 */
esp_err_t led_charge_task()
{
    // 1. 先尝试初始化（如果还未初始化）
    if (!led_initialized)
    {
        esp_err_t init_ret = led_charge_init();
        if (init_ret != ESP_OK)
        {
            ESP_LOGE(TAG, "LED charge initialization failed: %s", esp_err_to_name(init_ret));
            return init_ret; // 直接返回初始化失败的错误码
        }
    }

    // 2. 挂载文件系统（只执行一次）
    esp_err_t mount_ret = mount_storage(BASE_PATH);
    if (mount_ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to mount storage: %s", esp_err_to_name(mount_ret));
        return mount_ret;
    }

    // 3. 检查任务是否已经在运行
    if (charge_task_handle != NULL)
    {
        ESP_LOGW(TAG, "led_charge task already running");
        return ESP_OK;
    }

    // 4. 创建任务
    BaseType_t task_ret = xTaskCreate(
        led_charge_task_callback, // 任务函数
        "led_charge_task",        // 任务名称
        2048,                     // 堆栈大小
        NULL,                     // 任务参数
        5,                        // 任务优先级 (默认值)
        &charge_task_handle       // 任务句柄
    );

    if (task_ret != pdPASS)
    {
        ESP_LOGE(TAG, "failed to create led_charge task");
        charge_task_handle = NULL;
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "led_charge task started with %d ms interval", BLINK_INTERVAL_MS);
    return ESP_OK;
}
